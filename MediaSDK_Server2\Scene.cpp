﻿#include "Scene.h"
#include "ModeSceneMgr.h"

Scene::Scene() {}

Scene::~Scene() {}

void Scene::SetSceneID(UINT64 id)
{
	m_sceneInfo.id = id;	
}

void Scene::SetSceneInfo(const SCENE_INFO* sceneInfo)
{
	m_sceneInfo = *sceneInfo;
}

void Scene::SetParent(UINT64 id)
{
	m_sceneInfo.mode = (LIVE_MODE)id;
}

void Scene::AddCanvas(UINT64 desc, Canvas* canvas, UINT64 canvasID)
{
	if (!canvas)
		return;
	canvas->SetParent(m_sceneInfo.id);
    UINT32 videoModel = ModeSceneMgr::GetInstance()->GetModelByOldCanvasIdx(desc);
    canvas->SetVideoModel(videoModel);
	m_canvas.insert(std::pair<UINT64, Canvas*>(desc, canvas));
}

void Scene::RemoveCanvas(Canvas* canvas)
{
	if (!canvas)
		return;
	for (std::map<UINT64, Canvas*>::iterator it = m_canvas.begin(); it != m_canvas.end(); it++)
	{
		if (it->second == canvas)
		{
			canvas->Select(false);
			m_canvas.erase(it);
			break;
		}
	}
}

Canvas* Scene::GetCanvas(UINT64 desc)
{
	std::map<UINT64, Canvas*>::iterator it = m_canvas.find(desc);
	if (it == m_canvas.end())
		return 0;
	return it->second;
}

void Scene::Select(bool in, SWITCH_SCENE_TYPE type)
{
	for (int i = 0; i < 2; ++i)
	{
		Canvas* pCanvas = GetCanvas((LIVE_MODE)i);
		if (pCanvas)
		{
			if (type == SWITCH_SCENE_NORMAL)
			{
				pCanvas->Select(in);
			}
			else if (type == SWITCH_SCENE_PRELOAD)
			{
				pCanvas->PreLoadCanvas(in);
			}
			else if (type == SWITCH_SCENE_RELOAD)
			{
				pCanvas->ReLoadCanvas(in);
			}
			else if (type == SWITCH_SCENE_REPRELOAD)
			{
				pCanvas->RePreLoadCanvas(in);
			}
		}
	}
}

void Scene::GetSceneInfo(SCENE_INFO_EX* info, bool child /*= false*/)
{
	*(SCENE_INFO*)info = m_sceneInfo;
	if (child)
	{
		for (int i = 0; i < 2; i++)
		{
			Canvas* pCanvas = GetCanvas((LIVE_MODE)i);
			if (pCanvas)
			{
				CANVAS_INFO_EX canvasInfo = {};
				pCanvas->GetCanvasInfo(&canvasInfo, true);
				info->canvas.push_back(canvasInfo);
			}
		}
	}
}

void SceneLandscape::InitializeCanvas()
{
	CANVAS_INFO canvasInfo = {};
	canvasInfo.sceneID = m_sceneInfo.id;
	canvasInfo.desc = LIVE_MODE_LANDSCAPE;
	canvasInfo.rect = { .0f, .0f, 2.0f, 2.0f };
    canvasInfo.layoutRect = canvasInfo.rect;
    canvasInfo.fps = 60.0f;
    canvasInfo.bkColor = 0;
	canvasInfo.id = 0;
	UINT64 canvasID = ModeSceneMgr::GetInstance()->CreateCanvas(&canvasInfo, 0);
	Canvas* canvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	AddCanvas(canvasInfo.desc, canvas, canvasID);
}

void ScenePortrait::InitializeCanvas()
{
	CANVAS_INFO canvasInfo = {};
	canvasInfo.sceneID = m_sceneInfo.id;
	canvasInfo.desc = LIVE_MODE_PORTRAIT;
	canvasInfo.rect = { .0f, .0f, 2.0f, 2.0f };
	canvasInfo.layoutRect = canvasInfo.rect;
    canvasInfo.fps = 60.0f;
    canvasInfo.bkColor = 0;
	canvasInfo.id = 0;
	UINT64 canvasID = ModeSceneMgr::GetInstance()->CreateCanvas(&canvasInfo, 0);
	Canvas* canvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    AddCanvas(canvasInfo.desc, canvas, canvasID);
}

void SceneDBCanvas::InitializeCanvas()
{
	for (int i = 0; i < 2; i++)
	{
		CANVAS_INFO canvasInfo = {};
		canvasInfo.sceneID = m_sceneInfo.id;
		canvasInfo.desc = (LIVE_MODE)i;
		canvasInfo.rect = { .0f, .0f, 2.0f, 2.0f };
		canvasInfo.layoutRect = canvasInfo.rect;
		canvasInfo.fps = 60.0f;
		canvasInfo.bkColor = 0;
		canvasInfo.id = 0;
		UINT64 canvasID = ModeSceneMgr::GetInstance()->CreateCanvas(&canvasInfo, 0);
		Canvas* canvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
        AddCanvas(canvasInfo.desc, canvas, canvasID);
	}
}
