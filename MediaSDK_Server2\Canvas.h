﻿#pragma once

#include <vector>
#include "Layer.h"

enum CANVAS_MODE
{
	CANVAS_MODE_LANSSCAPE,
	CANVAS_MODE_PORTRAIT,
};

class Canvas
{
public:
	Canvas();
	~Canvas();

	void SetCanvasInfo(const CANVAS_INFO* info);
	void GetCanvasInfo(CANVAS_INFO_EX* info, bool child = false);
	void UpdateCanvasLayout(CANVAS_INFO* info);
	void SetParent(UINT64 id);
    void SetVideoModel(UINT32 videoModel);

	UINT64 AddLayerWithInfo(LAYER_INFO* layerInfo, bool calcLayout, const UINT64* id);
	bool   AddLayer(Layer* pLayer, bool calcLayout);
	void   RemoveLayer(Layer* layer);
	void   Select(bool in);
	void   SyncLayerSource(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo);
	void   PreLoadCanvas(bool in);

	void   ReLoadCanvas(bool in);
	void   RePreLoadCanvas(bool in, bool isReload = false);

	void   GetLayersOrder(std::vector<UINT64>* order);
	void   UpdateWholeLayersOrder(std::vector<UINT64> layerIDs, std::vector<UINT64>* moreIDs, std::vector<UINT64>* lessIDs);
	void   MoveLayerOrder(UINT64 layerID, MOVE_ORDER move);

	void   AddFilter(UINT64 id, FILTER* info);
    void   RemoveFilter(UINT64 id, FILTER* info);

protected:
	CANVAS_INFO m_canvasInfo;
	std::vector<UINT64> m_order;
	std::vector<Layer*> m_layers;
	bool m_syncMedia = false;
	UINT m_videoModel = UINT32_MAX;
};