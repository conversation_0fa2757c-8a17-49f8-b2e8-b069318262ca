﻿#pragma once

#include "Canvas.h"
#include <map>

enum SWITCH_SCENE_TYPE
{
	SWITCH_SCENE_NORMAL = 0,
	SWITCH_SCENE_PRELOAD = 1,
	SWITCH_SCENE_RELOAD = 2,
	SWITCH_SCENE_REPRELOAD = 3,
};

class Scene
{
public:
	Scene();
	virtual ~Scene();
	virtual void SetSceneID(UINT64 id);
	virtual void SetSceneInfo(const SCENE_INFO* sceneInfo);
	virtual void SetParent(UINT64 id);

	virtual void InitializeCanvas() {}
	virtual void AddCanvas(UINT64 desc, Canvas* canvas, UINT64 canvasID);
	virtual void RemoveCanvas(Canvas* canvas);
	virtual Canvas* GetCanvas(UINT64 desc);

	virtual void Select(bool in, SWITCH_SCENE_TYPE type);
	virtual void GetSceneInfo(SCENE_INFO_EX* info, bool child = false);

protected:
	SCENE_INFO m_sceneInfo;
	std::map<UINT64, Canvas*> m_canvas;
};

class SceneLandscape :public Scene
{
public:
	SceneLandscape() { m_sceneInfo.mode = LIVE_MODE_LANDSCAPE; }
	~SceneLandscape(){}
	virtual void InitializeCanvas();
	virtual Canvas* GetCanvas(UINT64 desc)
	{
		if (desc == LIVE_MODE_LANDSCAPE)
			return Scene::GetCanvas(desc);
		return 0;
	}
};

class ScenePortrait :public Scene
{
public:
	ScenePortrait() { m_sceneInfo.mode = LIVE_MODE_PORTRAIT; }
	~ScenePortrait() {}
	virtual void InitializeCanvas();
	virtual Canvas* GetCanvas(UINT64 desc)
	{
		if (desc == LIVE_MODE_PORTRAIT)
			return Scene::GetCanvas(desc);
		return 0;
	}
};

class SceneDBCanvas :public Scene
{
public:
	SceneDBCanvas() { m_sceneInfo.mode = LIVE_MODE_DBCANVAS; }
	~SceneDBCanvas() {}
	virtual void InitializeCanvas();
	virtual Canvas* GetCanvas(UINT64 desc)
	{
		if (desc == LIVE_MODE_LANDSCAPE || desc == LIVE_MODE_PORTRAIT)
			return Scene::GetCanvas(desc);
		return 0;
	}
};
