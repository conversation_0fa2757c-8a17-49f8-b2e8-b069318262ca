// Protocol Buffers - Google's data interchange format
// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

//////// State Message Manage -- async: 1.trigger 2.request ////////
package ls_state;
import "ls_base.proto";
import "ls_rtmp.proto";
import "ls_bytelink.proto";
import "ls_visual.proto";
import "ls_basicenum.proto";
import "ls_audio.proto";
import "ls_phonecamera.proto";
import "ls_camera.proto";

enum MOUSE_CLICK_EVENT
{
    MOUSE_EVENT_DOWN = 0;
    MOUSE_EVENT_UP = 1;
    MOUSE_CLICK_EVENT_VALID_AREA = 2;
	MOUSE_CLICK_EVENT_INVALID_AREA = 3;
};

enum CASTMATE_EVENT_TYPE {
	CASTMATE_EVENT_CLOSE = 0;
	CASTMATE_EVENT_FIRSTFRAME = 1;
	CASTMATE_EVENT_CONNECTED = 2; 
	CASTMATE_EVENT_DISCONNECTED = 3;
	CASTMATE_EVENT_PLUGIN = 4;         //4 and 5 are just for 
	CASTMATE_EVENT_PLUGOUT = 5;
	CASTMATE_EVENT_CASTMATE_EVENT = 6;
	CASTMATE_EVENT_ONERROR = 7;
	CASTMATE_EVENT_ONSDKERROR = 8;     //SDK Error
};

enum FAV_EVENT_TYPE
{
    FAV_EVENT_UNKNOW = 0;
    FAV_EVENT_OPENED = 1;
    FAV_EVENT_PAUSE_CHANGED = 2;
    FAV_EVENT_POSITION_CHANGED = 3;
    FAV_EVENT_READ_EOF = 4;
    FAV_EVENT_ERROR = 5;
    FAV_EVENT_FAIL_TO_OPEN = 6;
    FAV_EVENT_ERROR_EOF = 7;
    FAV_EVENT_PLAY_EOF = 8;
    FAV_EVENT_READ_PACKET_TIMEOUT = 9;
};

enum GAME_CAPTURE_EVENT_TYPE
{
    CAPTURE_SUCCESS_MEM = 0;
    CAPTURE_SUCCESS_TEXTURE = 1;
    CAPTURE_ERROR_TIME_OUT = 2;
    CAPTURE_ERROR_TEXTURE = 3;
    CAPTURE_ERROR_INITIALIZE = 4;
    CAPTURE_ERROR_INTERNAL = 5;    // inner error ,thread create error or mem alloc error
    CAPTURE_ERROR_HOOK = 6;        // hook dx api error
    CAPTURE_ERROR_DEVICE = 7;      // DX Device error
    CAPTURE_EVENT_COMMON = 8;      // 
    CAPTURE_EVENT_INJECT = 9;      // inject suc
    CAPTURE_EVENT_FIRSTFRAME = 10; // first frame
    CAPTURE_EVENT_DX9CPU_HOOK = 11;
    CAPTURE_EVENT_DX9GPU_HOOK = 12;
    CAPTURE_EVENT_DX10CPU_HOOK = 13;
    CAPTURE_EVENT_DX10GPU_HOOK = 14;
    CAPTURE_EVENT_DX101CPU_HOOK = 15;
    CAPTURE_EVENT_DX101GPU_HOOK = 16;
    CAPTURE_EVENT_DX11CPU_HOOK = 17;
    CAPTURE_EVENT_DX11GPU_HOOK = 18;
    CAPTURE_EVENT_DX12CPU_HOOK = 19;
    CAPTURE_EVENT_DX12GPU_HOOK = 20;
    CAPTURE_EVENT_GLCPU_HOOK = 21;
    CAPTURE_EVENT_GLGPU_HOOK = 22;
    CAPTURE_EVENT_VULKANCPU_HOOK = 23;
    CAPTURE_EVENT_VULKANGPU_HOOK = 24;
    CAPTURE_EVENT_STOP = 25;
    CAPTURE_ERROR_MEM_CREATE_ERROR = 26;
    CAPTURE_EVENT_VULKANCPU_HOOK_FAIL = 50;
    CAPTURE_EVENT_VULKANGPU_HOOK_FAIL = 51;
    CAPTURE_EVENT_VULKANALL_HOOK_FAIL = 52;
};

enum STREAM_OUTPUT_ACTION
{
	OUTPUT_ACTION_UNDEFINED = 0;
    // RTMP
    OUTPUT_ACTION_CONNECTING = 1;
    OUTPUT_ACTION_CONNECTED = 2;
    OUTPUT_ACTION_DISCONNECTING = 3;
    OUTPUT_ACTION_DISCONNECTED = 4;
    OUTPUT_ACTION_RECONNECTING = 5;
    OUTPUT_ACTION_ENCODE_FAILED = 6;

    // RTC
    OUTPUT_ACTION_RTC_STARTED = 7;
    OUTPUT_ACTION_RTC_STOPED = 8;

    // NDI
    OUTPUT_ACTION_NDI_STARTED = 9;
    OUTPUT_ACTION_NDI_STOPED = 10;

    // VIRTUAL
    OUTPUT_ACTION_VIRTUAL_STARTED = 11;
    OUTPUT_ACTION_VIRTUAL_STOPED = 12;
    OUTPUT_ACTION_FIRST_FRAME = 13;
    OUTPUT_ACTION_OPTIMIZE_IP = 14;
    OUTPUT_ACTION_RTS_TRACKLOG = 15;
    OUTPUT_ACTION_RTC_REOPEN = 16;
	OUTPUT_ACTION_EVENT_TRACK = 17;
	OUTPUT_ACTION_TRANSMIT_PULL_ERROR = 18;      // 读直播流到文件尾部
    OUTPUT_ACTION_TRANSMIT_NO_AUDIO_STREAM = 19; // 读取到的直播流没有音频
    OUTPUT_ACTION_TRANSMIT_NO_VIDEO_STREAM = 20; // 读取到的直播流没有视频
    OUTPUT_ACTION_TRANSMIT_AUDIO_VOLUME = 21;    // 读取到的音频流
	OUTPUT_ACTION_RTC_RECONNECTED = 22;
    OUTPUT_ACTION_RTC_LOST = 23;
    OUTPUT_ACTION_FALLBACK = 24;

	// RTC CONNECT STATE
    OUTPUT_ACTION_RTC_DISCONNECTED = 25;
    OUTPUT_ACTION_RTC_CONNECTING = 26;
    OUTPUT_ACTION_RTC_CONNECTED = 27;
    OUTPUT_ACTION_RTC_RECONNECTING = 28;
	OUTPUT_ACTION_FALLBACK_SUCCESS = 29;
};

enum STREAM_OUTPUT_CODE
{
    OUTPUT_SUCCESS = 0;
    OUTPUT_BAD_PATH = 1;
    OUTPUT_CONNECT_FAILED = 2;
    OUTPUT_INVALID_STREAM = 3;
    OUTPUT_DISCONNECTED = 4;
    OUTPUT_INVALID_VIDEO_CODEC = 5;
    OUTPUT_INVALID_PARAMETER = 6;
    OUTPUT_INVALID_CALL = 7;
    OUTPUT_UNSUPPORTED_FORMAT = 8;
    OUTPUT_FAILED = 9;
    OUTPUT_VIDEO_ENCODE_ERROR = 10;
    OUTPUT_MANUAL_RECONNECTION = 11;
    OUTPUT_CONNECT_RTMPS_FAILED = 12;
    OUTPUT_CONNECT_RTMPS_FAILED_CERT_VERIFY = 13;
    OUTPUT_CONNECT_DNS_ERROR = 14;
    OUTPUT_RECONNECT_OVERFLOW = 15;
    OUTPUT_INVALID_AUDIO_CODEC = 16;
};

enum REMOTE_LEAVE_REASON 
{
    REMOTE_LEAVE_REASON_QUIT = 0;
    REMOTE_LEAVE_REASON_DROPPED = 1;
    REMOTE_LEAVE_REASON_SWITCH_TO_INVISIBLE = 2;
    REMOTE_LEAVE_REASON_KICKED_BY_ADMIN = 3;
};

enum STREAM_MIXING_EVENT {
    STREAM_MIXING_EVENT_BASE = 0;
    STREAM_MIXING_EVENT_START = 1;
    STREAM_MIXING_EVENT_START_SUCCEED = 2;
    STREAM_MIXING_EVENT_START_FAILED = 3;
    STREAM_MIXING_EVENT_UPDATE = 4;
    STREAM_MIXING_EVENT_UPDATE_SUCCEED = 5;
    STREAM_MIXING_EVENT_UPDATE_FAILED = 6;
    STREAM_MIXING_EVENT_STOP = 7;
    STREAM_MIXING_EVENT_STOP_SUCCEED = 8;
    STREAM_MIXING_EVENT_STOP_FAILED = 9;
    STREAM_MIXING_EVENT_CHANGE_MIX_TYPE = 10;
    STREAM_MIXING_EVENT_FIRST_AUDIO_FRAME_CLIENT_MIX = 11;
    STREAM_MIXING_EVENT_FIRST_VIDEO_FRAME_CLIENT_MIX = 12;
    STREAM_MIXING_EVENT_UPDATE_TIMEOUT = 13;
    STREAM_MIXING_EVENT_START_TIMEOUT = 14;
    STREAM_MIXING_EVENT_REQUEST_PARAM_ERROR = 15;
    STREAM_MIXING_EVENT_MIX_IMAGE_EVENT = 16;
    STREAM_MIXING_EVENT_MIX_SINGLE_WAY_CHORUS_EVENT = 17;
    STREAM_MIXING_EVENT_MIX_STREAM_MIXING_MAX = 18;
    STREAM_MIXING_EVENT_ALTERNATE_IMAGE_SUCCEED = 19;
    STREAM_MIXING_EVENT_ALTERNATE_IMAGE_FAILED = 20;
    STREAM_MIXING_EVENT_Background_URL_SUCCEED = 21;
    STREAM_MIXING_EVENT_Background_URL_FAILED = 22;
};

enum DEVICE_TRANSPORT_TYPE
{
    DEVICE_TRANSPORT_UNKNOWN = 0;
    DEVICE_TRANSPORT_BUILT_IN = 1;
    DEVICE_TRANSPORT_BLUETOOTH = 2;
    DEVICE_TRANSPORT_VIRTUAL = 3;
    DEVICE_TRANSPORT_USB = 4;
    DEVICE_TRANSPORT_DISPLAY_AUDIO = 5;
    DEVICE_TRANSPORT_PCI = 6;
    DEVICE_TRANSPORT_AIR_PLAY = 7;
};

enum AUDIO_DATA_FLOW
{
    AUDIO_DATA_FLOW_INPUT = 0;
    AUDIO_DATA_FLOW_OUTPUT = 1;
    AUDIO_DATA_FLOW_ALL = 2;
};

enum AUDIO_DEVICE_ROLE
{
    AUDIO_DEVICE_ROLE_CONSOLE = 0;
    AUDIO_DEVICE_ROLE_MULTI_MEDIA = 1;
    AUDIO_DEVICE_ROLE_COMMUNICATIONS = 2;
};

enum AUDIO_DEVICE_STATE
{
    AUDIO_DEVICE_STATE_ALL = 0;
    AUDIO_DEVICE_STATE_ACTIVE = 1;
    AUDIO_DEVICE_STATE_DISABLE = 2;
    AUDIO_DEVICE_STATE_NOTPRESENT = 4;
    AUDIO_DEVICE_STATE_UNPLUGGED = 8;
};

enum VISUAL_CAPTURE_TYPE
{
    CAPTURE_TYPE_NONE = 0;
    CAPTURE_TYPE_DDA = 1;
    CAPTURE_TYPE_WGC = 2;
    CAPTURE_TYPE_BITBLT = 3;
    CAPTURE_TYPE_GAME_INJECT_TEXTURE = 4;
    CAPTURE_TYPE_GAME_INJECT_MEMORY = 5;
}

enum THREAD_ID_INFO
{
    THREAD_ID_RENDER = 0;
}

enum THREAD_MONITOR_EVENT_TYPE
{
    THREAD_MONITOR_START = 0;
    THREAD_MONITOR_TICK = 1;
    THREAD_MONITOR_END = 2;
}

enum CURSOR_HIT_POSITION
{
    HIT_POSITION_NOTHING = 0;
    HIT_POSITION_TOP_LEFT = 1;
    HIT_POSITION_TOP_RIGHT = 2;
    HIT_POSITION_BOTTOM_RIGHT = 3;
    HIT_POSITION_BOTTOM_LEFT = 4;
    HIT_POSITION_TOP = 5;
    HIT_POSITION_RIGHT = 6;
    HIT_POSITION_BOTTOM = 7;
    HIT_POSITION_LEFT = 8;
    HIT_POSITION_MIDDLE = 9;
}

enum SHORT_CUT_ACTION
{
    ACTION_KEY_NONE = 0;
    ACTION_KEY_UP = 1;
    ACTION_KEY_DOWN = 2;
    ACTION_KEY_LEFT = 3;
    ACTION_KEY_RIGHT = 4;
    ACTION_KEY_ALT_CLIP = 5;
    ACTION_KEY_SHIFT_STRETCH = 6;
}

enum DEVICEFOUNDOPT 
{
    DEVICEFOUNDOPT_ADD = 0;
    DEVICEFOUNDOPT_DEL = 1;
}

enum AUDIO_SOURCE_WARNING_TYPE
{
    NO_WARNING = 0;
    SILENT_FRAME = 1;
    NO_FRAME = 2;
    SILENT_FRAME_RECOVER = 3;
    NO_FRAME_RECOVER = 4;
}

message AudioPeakInfo {
    string audio_id = 1;
    repeated float peak = 2;           // [left, right]
    repeated float device_peak = 3;    // [left_dev, right_dev]
    bool isTrack = 4;
}

message ForwardStreamStateInfo {
    string room_id = 1;
    sint32 forward_state_mask = 2;
    sint32 forward_error_mask = 3;
}


message NetWorkStats{
    string uid = 1;
    float  fraction_lost  = 2;
    int32  rtt = 3;
    int32  total_bandwidth = 4;
    int32  tx_quality = 5;
    int32  rx_quality = 6;
}

message MinScaleInfo {
	string vid = 1;
	ls_base.ScaleF min_scale = 2;
	ls_base.SizeS size = 3;
}

message Callback {
    message OnInitEvent {
        bool success = 1;
        sint32 dx_error = 2;
        uint32 pid = 3;
    }

    message Visual {
        message OnVisualCreatedEvent {
            string visual_id = 1;
            ls_visual.VISUAL_TYPE visual_type = 2;
            bool success = 3;
            ls_basicenum.CREATE_SOURCE_FAILED_REASON reason = 4;
            uint32 error_code = 5;
        }

         message OnVisualFallbackEvent {
            string visual_id = 1;
            ls_visual.VISUAL_TYPE visual_type = 2;
            bool success = 3;
            ls_basicenum.CREATE_SOURCE_FAILED_REASON reason = 4;
            uint32 error_code = 5;
            bool fallback_to_placeholder = 6;
        }

        message OnVisualDeletedEvent {
		    string visual_id = 1;
            bool success = 2;
	    }

        message OnVisualTransformChangedEvent {
            string visual_id = 1;
            ls_base.Transform transform = 2;
            ls_base.SizeF canvas_size = 3;
            ls_base.SizeF visual_size = 4;
            ls_visual.VISUAL_LAYOUT layout = 5;
        }

        message OnBeginTrackEvent {
		    string visual_id = 1;
            CURSOR_HIT_POSITION hit_pos = 2;
	    }    

	    message OnEndTrackEvent {
		    string visual_id = 1;
            CURSOR_HIT_POSITION hit_pos = 2;
	    }

        message OnClipMaskEndEvent {
            string visual_id = 1;
        }

	    message OnSelectChangeEvent {
		    string visual_id = 1;
		    uint32 video_model_id = 2;
            bool manual = 3;
	    }

	    message OnStateChangeEvent {
		    string visual_id = 1;
		    bool state = 2;
	    }

        message OnShortcutActionEvent {
	    	string visual_id = 1;
	    	SHORT_CUT_ACTION action = 2;
	    }

	    message OnMouseClickEvent {
		    MOUSE_CLICK_EVENT type = 1;
		    string visual_id = 2;
	    }

        message OnMouseDoubleClickEvent {
            string visual_id = 1;
            uint32 video_model_id = 2;
        }

        // Camera
        message OnColorRangeDetectEvent {
	    	string visual_id = 1;
	    	ls_basicenum.VIDEO_RANGE result = 2;
	    }

        message OnCameraControlEvent {
            string visual_id = 1;
            ls_camera.CAMERA_CONTROL_TYPE control_type = 2;
            bool state = 3;
        }
        
        // Bytelink
        message OnCastMateEvent {
	        CASTMATE_EVENT_TYPE castmate_event = 1;
	        ls_bytelink.CASTMATE_PROTOCOL_TYPE protocol = 2; 
	        uint32 event_code = 3;      //only valid when event == CASTMATE_EVENT
	        string msg = 4;             //only valid when event == CASTMATE_EVENT
        }

        // Fav
        message OnFAVEvent {
		    string visual_id = 1;
		    FAV_EVENT_TYPE event_type = 2;
            bool is_animation = 3;
	    }

        // Game
        message OnGameEvent {
            string visual_id = 1;
            GAME_CAPTURE_EVENT_TYPE capture_event = 2;
            uint64 ts = 3;
            string info = 4;
	    }

        message OnGameSourceProcessCrashDetectEvent {
            string visual_id = 1;
            string exe_name = 2;
            uint32 pid = 3;
            uint32 exit_code = 4;
            bool   exited = 5;
        }

        message OnFullScreenDetectorEvent {
            string detector_id = 1;
            bool   found_target = 2;
            int64  win_id = 3;
            string class_name = 4;
            string exe_name = 5;
            uint32 pid = 6;
        }

        // Graffiti
        message OnTracksInfoChangeEvent {
	        string visual_id = 1;
	        string tracks_info = 2;
        }

        message OnVisualReadyEvent {
            string visual_id = 1;
            ls_base.SizeF size = 2;
        }

        message OnVisualCaptureTypeChangeEvent {
            string visual_id = 1;
            VISUAL_CAPTURE_TYPE type = 2;
            uint32 capture_pid = 3;
            uint64 capture_hwnd = 4;
        }

        // PhoneCamera
        message OnStartResult {
            uint32 err_code = 1;
            string ip_list = 2;
            uint32 port = 3;
        } 

        message OnConnect {
            uint32 err_code = 1;
            ls_phonecamera.PHONECAMERA_DEVICE_INFO info = 2; 
        }

        message OnCameraReadyResult {
            uint32 err_code = 1;
        }

        message OnClosed {
        }

        message OnDeiviceFound {
            uint32 err_code = 1;
            DEVICEFOUNDOPT opt = 2;
            ls_phonecamera.PHONECAMERA_DEVICE_INFO info = 3;
        }

        message OnDisconnect {
            uint32 err_code = 1;
            string msg = 2;
            string device_key = 3;
        }

        message OnError {
            uint32 err_code = 1;
            string msg = 2;
        }

        message OnRecvMetaData{
            string src = 1;
            string msg = 2;
        }

        message OnCreateFrameStatue {
            string frame_id = 1;
            ls_base.SizeF frame_size = 2;
            int32 err_code = 3;
            string err_msg = 4;
        }
    }

    // Audio
    message Audio {
        message OnAudioPeakEvent {
	        repeated AudioPeakInfo info = 1;
        }

        message OnAudioDefaultChangeEvent {
	        string device_id = 1;
	        AUDIO_DATA_FLOW data_flow = 2;
            AUDIO_DEVICE_ROLE device_role = 3;
            string device_name = 4;
        }

        message OnAudioDeviceStateChangeEvent {
	        string device_id = 1;
	        AUDIO_DEVICE_STATE device_state = 2;
        }

        message OnAudioDeviceAddEvent {
            string device_id = 1;
        }

        message OnAudioDeviceRemoveEvent {
            string device_id = 1;
        }

        message OnAudioDevicePropertyChangeEvent {
            string device_id = 1;
        }

        message OnAudioCapableAppChangedEvent {
        }

        message OnPCMAudioEOFEvent {
            string audio_id = 1;
        }

        message OnPCMAudioBreakEvent {
            string audio_id = 1;
            uint32 left_sample_cnt = 2;
        }

        message OnEchoDetectionResultEvent {
            float probability = 1;
        }

        message OnWASAPIDeviceInfoEvent {
        	string device_id = 1;
        	string audio_id = 2;
        	DEVICE_TRANSPORT_TYPE type = 3;
        	bool mic = 4;
            bool speaker = 5;
            bool system_mute = 6;
            float system_capture_volume = 7;
        }

        message OnWASAPIEnableRawDataModeFailedEvent {
        	string device_id = 1;
        	string audio_id = 2;
        	sint32 hresult = 3;
        }
        
        message OnWASAPIGetMicrophoneBoostFailedEvent {
        	string device_id = 1;
        	string audio_id = 2;
        	sint32 hresult = 3;
        }

        message OnAudioBufferMisalignmentEvent {
            string audio_id = 1;
            uint32 too_early = 2;
            uint32 too_late = 3;
        }

        message OnAudioFailedStatusEvent {
            string audio_id = 1;
            int32 error_code = 2;
            string error_msg = 3;
            int32 hresult = 4;
            ls_audio.AUDIO_TYPE audio_type = 5;
        }

        message OnAudioSourceFormatEvent {
            string audio_id = 1;
            ls_basicenum.AUDIO_FORMAT format = 2;
            uint32 channel = 3;
            uint32 sample_rate = 4;
            ls_basicenum.AUDIO_CHANNEL_LAYOUT layout = 5;
            uint32 block_size = 6;
        }

        message OnAudioSourceErrorFormatEvent {
            string audio_id = 1;
            uint32 channel = 2;
            uint32 channel_mask = 3;
            uint32 sample_rate = 4;
            uint32 bits_per_sample = 5;
        }

        message OnAudioSystemDeviceVolumeEvent {
            float system_capture_volume = 1;
            bool system_mute = 2;
            string audio_id = 3;
        }

        message OnAudioSourceWarningEvent {
            string audio_id = 1;
            AUDIO_SOURCE_WARNING_TYPE warning_code = 2;
        }

        message OnAudioLyraxRawDataResult {
            int32 raw_data_api_option = 1;
            int32 raw_data_api_decision_option = 2;
            int32 apply_raw_data_option = 3;
            int32 system_state = 4;
            bool apply_result = 5;
            string audio_id = 6;
        }

        message OnAudioDeviceCaptureRateCheckResult {
            string audio_id = 1;
            int32 raw_rate = 2;
            int32 diff_rate = 3;
        }
    }

    message Filter {
        // Effect
        message OnEffectStateEvent {
            string visual_id = 1;
            bool valid = 2;
        }

        message OnEffectResourceLoadEvent {
            string visual_id = 1;
	        uint64 msg_id = 2;
	        int64 arg1 = 3;
	        int64 arg2 = 4;
	        string arg3 = 5;
            string effect_first_frame_elapsed_time = 6;
            string effect_post_first_frame_elapsed_time = 7;
        }

        message OnEffectMsgEvent {
            string visual_id = 1;
            uint64 msg_id = 2;
            int64 arg1 = 3;
            int64 arg2 = 4;
            string arg3 = 5;
        }

        message OnEffectPlatformEvent {
		    string serialized_json = 1;
	    }

        message OnCommonMetricsEvent {
		    string filter_id = 1;
            repeated string audio_ids = 2;
		    string common_metrics = 3;
	    }

        message OnEffectGLVersionEvent {
        	int64 version = 1;
        }

      message OnEffectLagEvent {
        string visual_id = 1;
        string effect_info = 2;
        bool is_first_frame = 3;
        int64 time = 4;
        int32 cost = 5;
        int32 type = 6;
      }
    }

    // Canvas
    message Canvas {
        message OnMouseHoverEvent {
	        uint32 video_model_id = 1;
        }

        message OnMouseLeaveEvent {
	        uint32 video_model_id = 1;
        }
    }

    // RTMP
    message RTMP {
        message OnABRBitrateChangeEvent {
	        string stream_id = 1;
            sint32 cur_bitrate = 2;
            sint32 prev_bitrate = 3;
        }

        message OnStreamEvent {
	        string stream_id = 1;
	        STREAM_OUTPUT_ACTION action = 2;
	        STREAM_OUTPUT_CODE code = 3;
            string extra = 4;                            // extraInfo. could be json or plain string
	        sint64 time_epoch = 5;
	        ls_rtmp.STREAM_TYPE cur_type = 6;
	        ls_rtmp.STREAM_TYPE fallback_type = 7;
        }

        message OnBandwidthEvent {
	        string stream_id = 1;
	        ls_rtmp.STREAM_TYPE stream_type = 2;
	        float average_transport_bitrate = 3;
	        float prob_rtt = 4;
	        float prob_bandwidth = 5;
	        sint32 total_sends = 6;
	        sint32 total_drops = 7;
	        sint32 total_duration = 8;
	        sint32 total_send_duration = 9;
	        ls_rtmp.RTMP_UPLOAD_SPEED_TEST_RESULT result = 10;
	        ls_rtmp.RTMP_UPLOAD_SPEED_TEST_FAIL_REASON failed_reason = 11;
        }

        message OnStreamEncodeEvent {
            string stream_id = 1;
            string json_info = 2;
        }
    }

    // RTC
    message RTC {
        message OnJoinChannel {
            string channel = 1;
            string uid = 2;
            int32 error_code = 3;
            bool   first_time = 4;
        }

        message OnLeaveChannel {
        }

        message OnRemoteEnter {
            string uid = 1;
            int32  elapsed = 2;
        }

        message OnRemoteLeave {
            string uid = 1;
            REMOTE_LEAVE_REASON elapsed = 2;
        }

        message OnEngineStart {
            int32 ret = 1;
            int32 error_code = 2;
        }

        message OnEngineStop {
        }

        message OnRoomMessageReceived {
            string uid = 1;
            string msg = 2;
        }

        message OnStreamMixing {
            string task_id = 1;
            STREAM_MIXING_EVENT event_type = 2;
            int32 error = 3;
        }

        message OnUserMessageReceived {
            string uid = 1;
            string msg = 2;
        }

        message OnRemoteVideoSizeChange {
            string uid = 1;
            int32 stream_index = 2;
            int32 width = 3;
            int32 height = 4;
            int32 rotation = 5;
        }

        message OnRemoteFirstVideoFrameDecoded {
            string uid = 1;
            int32 stream_index = 2;
            int32 width = 3;
            int32 height = 4;
            int32 rotation = 5;
        }

        message OnRemoteFirstAudioFrame {
            string uid = 1;
            int32 stream_index = 2;
        }

        message OnRemoteFirstVideoFrameRender {
            string uid = 1;
            int32 stream_index = 2;
            int32 width = 3;
            int32 height = 4;
            int32 rotation = 5;
        }

        message OnLocalAudioVolumeIndication {
          string json_content = 1;
        }

        message OnRemoteAudioVolumeIndication {
          string json_content = 1;
        }

        message OnStreamStats {
            string uid = 1;
            int32 stream_index = 2;
            int32 video_send_target_bitrate = 3;
            int32 video_send_bitrate = 4;
            int32 video_sent_rate = 5;
            int32 video_loss_rate = 6;
            int32 video_encoder_target_rate = 7;
	        int32 video_encoder_rate = 8;
	        int32 local_tx_quality = 9;
            int32 width = 10;
            int32 height = 11;
            int32 codec_type = 12;
        }

        message OnNetworkStats {
            repeated NetWorkStats infos = 1;
        }

        message OnError {
            int32 err = 1;
        }

        message OnConnectionState {
            int32 state = 1;
        }

        message OnWarning {
            int32 warn = 1;
        }

        message OnConnectionLost {
        }

        message OnActiveSpeaker {
            string uid = 1;
        }

        message OnForwardStreamStateInfo {
            repeated ForwardStreamStateInfo infos = 1;
        }
    }

    // Statistic
    message Statistic {
        message OnTeaEvent {
	        string name = 1;
   	        string parames = 2;
        }

        message OnGPUDetectEvent {
        }

        message OnDevLostEvent {
	        string str = 1;
	        int32  error = 2;
	        string remove_reason = 3;
	        bool   effect = 4;
	        string driver_date = 5;
	        string driver_name = 6;
	        string driver_ver = 7;
	        string pre_driver_date = 8;
	        string pre_driver_name = 9;
	        string pre_driver_ver = 10;
	        bool display_change = 11;
        }

        message OnVqosDataReportEvent {
            string data = 1;
        }
    }

    message Log {
        message LogEvent {
        }
    }

    message OnThreadMonitorEvent {
	    THREAD_ID_INFO thread_id = 1;
	    THREAD_MONITOR_EVENT_TYPE event_type = 2;
    }

    message OnColorPickerPixelEvent {
        uint32 color = 1;    // color value, as a 4-bytes argb format, each byte corresponding to a uint32 value out of 255. For example,0x00FFFFFF equals (A,R,G,B)=(0.0,1.0,1.0,1.0)
    }
}